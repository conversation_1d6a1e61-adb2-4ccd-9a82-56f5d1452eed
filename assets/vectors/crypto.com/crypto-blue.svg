<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 1000 1000" xml:space="preserve">
<desc>Created with Fabric.js 3.5.0</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="#ffffff"/>
<g transform="matrix(6.448 0 0 6.448 494.0604 494.0623)" id="751519" clip-path="url(#CLIPPATH_7)">
<clipPath id="CLIPPATH_7">
	<rect transform="matrix(1 0 0 1 0.0001 0.0002)" id="clip0_302_100" x="-79" y="-79" rx="0" ry="0" width="158" height="158"/>
</clipPath>
<path style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(3,49,108); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-78.9999, -78.9998)" d="M 154.685 0.000367219 H 3.31409 C 1.48378 0.000367219 0 1.48415 0 3.31478 V 154.686 C 0 156.516 1.48378 158 3.31409 158 H 154.685 C 156.515 158 157.999 156.516 157.999 154.686 V 3.31478 C 158 3.26912 158 3.22347 157.999 3.17813 C 157.962 1.38523 156.478 -0.0373614 154.685 0.000367219 Z" stroke-linecap="round"/>
</g>
<g transform="matrix(5.0526 -2.9196 2.9041 5.0259 500.6682 499.4119)" id="736880" clip-path="url(#CLIPPATH_8)">
<clipPath id="CLIPPATH_8">
	<rect transform="matrix(1 0 0 1 0 0.0002)" id="clip0_302_52" x="-79.5" y="-79" rx="0" ry="0" width="159" height="158"/>
</clipPath>
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-79.5, -78.9998)" d="M 158.553 77.2093 L 120.406 11.5305 C 119.865 10.5695 118.861 9.95602 117.754 9.90882 H 41.6641 C 40.4877 9.89837 39.3963 10.5179 38.8082 11.5305 L 0.457077 77.412 C -0.152359 78.4326 -0.152359 79.7024 0.457077 80.7229 L 38.8082 146.672 C 39.4144 147.538 40.399 148.065 41.4601 148.091 H 117.754 C 118.818 148.076 119.808 147.546 120.406 146.672 L 158.553 80.5202 C 159.149 79.4958 159.149 78.234 158.553 77.2093 Z" stroke-linecap="round"/>
</g>
<g transform="matrix(7.8914 0 0 7.8914 499.9993 499.9991)" id="132068">
<g style="" vector-effect="non-scaling-stroke">
		<g transform="matrix(1 0 0 1 0 0)" id="Path">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" transform=" translate(-326.1, -326.9)" d="M 326.100006 269.299988 L 276.200012 298.100006 L 276.200012 355.700012 L 326.100006 384.5 L 376 355.700012 L 376 298.100006 L 326.100006 269.299988 Z M 326.100006 269.299988 L 276.200012 298.100006 L 276.200012 355.700012 L 326.100006 384.5 L 376 355.700012 L 376 298.100006 L 326.100006 269.299988 Z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 0 0)" id="path1">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(3,49,108); fill-rule: nonzero; opacity: 1;" transform=" translate(-326.1, -326.9)" d="M 345.800018 362 L 338.700012 362 L 330.200012 354.200012 L 330.200012 350.200012 L 339 341.799988 L 339 328.5 L 350.5 321 L 363.600006 330.899994 L 345.800018 362 Z M 316.399994 341 L 317.700012 328.5 L 313.399994 317.299988 L 338.800018 317.299988 L 334.600006 328.5 L 335.800018 341 L 326 341 L 316.399994 341 Z M 322.200012 354.200012 L 313.700012 362.100006 L 306.5 362.100006 L 288.600006 330.899994 L 301.799988 321.100006 L 313.399994 328.5 L 313.399994 341.799988 L 322.200012 350.200012 L 322.200012 354.200012 Z M 306.399994 294.200012 L 345.700012 294.200012 L 350.399994 314.200012 L 301.799988 314.200012 L 306.399994 294.200012 Z M 326.100006 269.299988 L 276.200012 298.100006 L 276.200012 355.700012 L 326.100006 384.5 L 376 355.700012 L 376 298.100006 L 326.100006 269.299988 Z" stroke-linecap="round"/>
</g>
</g>
</g>
</svg>
