import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:openotp/widgets/custom_app_bar.dart';
import 'package:provider/provider.dart';
import 'package:forui/forui.dart';
import '../models/otp_entry.dart';
import '../models/settings_model.dart';
import '../services/otp_service.dart';
import '../services/secure_storage_service.dart';
import '../services/logger_service.dart';
import '../services/qr_scanner_service.dart';
import '../services/icon_service.dart';
import '../services/theme_service.dart';
import '../services/auth_service.dart';
import '../services/app_reload_service.dart';
import '../utils/route_generator.dart';

// Enum for the FAB menu options
enum FabOption { scanQr, scanQrFromImage, manualEntry }

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final SecureStorageService _storageService = SecureStorageService();
  final OtpService _otpService = OtpService();
  final LoggerService _logger = LoggerService();
  final QrScannerService _qrScannerService = QrScannerService();
  late final IconService _iconService; // Will be initialized from Provider
  final AuthService _authService = AuthService();
  final AppReloadService _reloadService = AppReloadService();
  List<OtpEntry> _otpEntries = [];
  Timer? _timer;
  int _secondsRemaining = 30;
  bool _isEditMode = false;

  // Cache for icon widgets to avoid rebuilding them on every timer tick
  final Map<String, Widget> _iconCache = {};

  // Cache for TOTP codes to avoid regenerating them on every timer tick
  final Map<String, String> _totpCache = {};

  // Selected OTP entry for Authy-style view
  String? _selectedOtpId;

  // Subscriptions for reload events
  StreamSubscription? _otpEntriesReloadSubscription;
  StreamSubscription? _fullAppReloadSubscription;

  @override
  void initState() {
    super.initState();
    _logger.i('Initializing HomeScreen');

    // Get the IconService from Provider
    _iconService = Provider.of<IconService>(context, listen: false);
    _logger.d('IconService obtained from Provider');

    // Set up listeners for reload events
    _setupReloadListeners();

    _checkAndCleanupInvalidEntries();
    _startTimer();
  }

  // Check for and remove any invalid OTP entries
  Future<void> _checkAndCleanupInvalidEntries() async {
    _logger.d('Checking for invalid OTP entries');
    try {
      // Run the cleanup
      final removedCount = await _storageService.cleanupInvalidEntries();

      // Show a notification if any entries were removed
      if (removedCount > 0 && mounted) {
        // Delay the snackbar to ensure the UI is built
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Removed $removedCount invalid OTP ${removedCount == 1 ? 'entry' : 'entries'}')));
          }
        });
      }

      // Load the entries after cleanup
      await _loadOtpEntries();
    } catch (e, stackTrace) {
      _logger.e('Error checking for invalid OTP entries', e, stackTrace);
      // Still try to load entries even if cleanup failed
      await _loadOtpEntries();
    }
  }

  // Select the first OTP entry when entries are loaded
  void _selectFirstOtpIfNeeded() {
    if (_otpEntries.isNotEmpty && _selectedOtpId == null) {
      setState(() {
        _selectedOtpId = _otpEntries.first.id;
      });
    }
  }

  // Set up listeners for reload events
  void _setupReloadListeners() {
    _logger.d('Setting up reload listeners for HomeScreen');

    // Listen for OTP entries reload events
    _otpEntriesReloadSubscription = _reloadService.onOtpEntriesReload.listen((_) {
      _logger.i('OTP entries reload event received');
      _loadOtpEntries();
    });

    // Listen for full app reload events
    _fullAppReloadSubscription = _reloadService.onFullAppReload.listen((_) {
      _logger.i('Full app reload event received');
      _loadOtpEntries();
    });
  }

  @override
  void dispose() {
    _logger.i('Disposing HomeScreen');
    _timer?.cancel();
    _otpEntriesReloadSubscription?.cancel();
    _fullAppReloadSubscription?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _logger.d('Starting OTP refresh timer');

    // Generate initial OTP codes for all entries
    _generateAllOtpCodes();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_otpEntries.isNotEmpty) {
        // Find the first TOTP entry to use for the timer
        // If there are no TOTP entries, use the first entry's period as a fallback
        final totpEntry = _otpEntries.firstWhere((entry) => entry.type == OtpType.totp, orElse: () => _otpEntries.first);

        final secondsRemaining = _otpService.getRemainingSeconds(totpEntry);

        setState(() {
          _secondsRemaining = secondsRemaining;
        });

        // Refresh codes when timer reaches 0
        if (secondsRemaining == totpEntry.period) {
          _logger.d('Timer reached 0, refreshing TOTP codes');
          // Regenerate all OTP codes
          _generateAllOtpCodes();
          setState(() {}); // Trigger rebuild to update UI
        }
      }
    });
  }

  // Generate OTP codes for all entries and store in cache
  Future<void> _generateAllOtpCodes() async {
    _logger.d('Generating OTP codes for all entries');
    for (final entry in _otpEntries) {
      String code;
      if (entry.type == OtpType.totp) {
        code = _otpService.generateTotp(entry);
      } else {
        // For HOTP, we don't auto-generate codes, we just use the cached one or show a placeholder
        if (_totpCache.containsKey(entry.id)) {
          code = _totpCache[entry.id]!;
        } else {
          code = 'TAP TO GENERATE';
        }
      }
      _totpCache[entry.id] = code;

      // Log appropriate message based on code generation result
      if (code == 'ERROR') {
        _logger.w('Failed to generate OTP code for ${entry.name} - invalid secret key');
      } else if (code != 'TAP TO GENERATE') {
        _logger.d('Generated OTP code for ${entry.name}: $code');
      }
    }
  }

  Future<void> _loadOtpEntries() async {
    _logger.d('Loading OTP entries');
    try {
      final entries = await _storageService.getOtpEntries();
      setState(() {
        _otpEntries = entries;
        // Clear the caches when entries are reloaded
        _iconCache.clear();
        _totpCache.clear();
      });
      _logger.i('Loaded ${entries.length} OTP entries');

      // Generate new OTP codes for the loaded entries
      if (entries.isNotEmpty) {
        await _generateAllOtpCodes();
        _selectFirstOtpIfNeeded();
      }
    } catch (e, stackTrace) {
      _logger.e('Error loading OTP entries', e, stackTrace);
    }
  }

  Future<void> _addOtpEntry() async {
    _logger.d('Navigating to add OTP entry screen for manual entry');
    try {
      if (!mounted) return;

      final result = await Navigator.pushNamed<bool>(context, RouteGenerator.addOtp, arguments: {'showQrOptions': false});

      if (result == true) {
        _logger.i('New OTP entry added, reloading entries');
        await _loadOtpEntries();
      } else {
        _logger.d('Add OTP entry cancelled or failed');
      }
    } catch (e, stackTrace) {
      _logger.e('Error navigating to add OTP entry screen', e, stackTrace);
    }
  }

  Future<void> _scanQrCode() async {
    _logger.d('Starting QR code scanning');
    try {
      if (!mounted) return;

      if (_qrScannerService.isCameraQrScanningSupported()) {
        // Navigate to the AddOtpScreen with QR scanning enabled
        final result = await Navigator.pushNamed<bool>(context, RouteGenerator.addOtp, arguments: {'initiallyShowQrScanner': true});

        if (result == true) {
          _logger.i('New OTP entry added from QR scan, reloading entries');
          await _loadOtpEntries();
        } else {
          _logger.d('QR scan cancelled or failed');
        }
      } else {
        // Show message for unsupported platforms
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(_qrScannerService.getUnsupportedCameraMessage())));

        // Offer to scan from image instead
        _scanQrFromImage();
      }
    } catch (e, stackTrace) {
      _logger.e('Error during QR code scanning', e, stackTrace);
    }
  }

  Future<void> _scanQrFromImage() async {
    _logger.d('Starting QR scan from image');
    try {
      if (!mounted) return;

      final qrCode = await _qrScannerService.pickAndDecodeQrFromImage();

      if (qrCode != null) {
        // Navigate to AddOtpScreen with the scanned QR code
        if (!mounted) return;
        final result = await Navigator.pushNamed<bool>(context, RouteGenerator.addOtp, arguments: {'initialQrCode': qrCode});

        if (result == true) {
          _logger.i('New OTP entry added from image QR scan, reloading entries');
          await _loadOtpEntries();
        } else {
          _logger.d('Image QR scan processing cancelled or failed');
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('No QR code found in the selected image')));
        }
      }
    } catch (e, stackTrace) {
      _logger.e('Error scanning QR from image', e, stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error scanning QR code: ${e.toString()}')));
      }
    }
  }

  Future<void> _deleteOtpEntry(String id) async {
    _logger.d('Attempting to delete OTP entry with ID: $id');
    try {
      // Find the entry to get its name for confirmation
      final entry = _otpEntries.firstWhere((entry) => entry.id == id);
      final entryName = entry.issuer.isNotEmpty ? '${entry.issuer} (${entry.name})' : entry.name;

      // Show confirmation dialog
      final confirmed = await _showDeleteConfirmationDialog(entryName);

      if (confirmed) {
        _logger.i('Deletion confirmed for OTP entry: $entryName');

        // Remove the entry from caches
        _iconCache.remove(id);
        _totpCache.remove(id);

        await _storageService.deleteOtpEntry(id);
        _logger.i('OTP entry deleted, reloading entries');
        await _loadOtpEntries();
      } else {
        _logger.d('Deletion cancelled for OTP entry: $entryName');
      }
    } catch (e, stackTrace) {
      _logger.e('Error deleting OTP entry with ID: $id', e, stackTrace);
    }
  }

  // Show a confirmation dialog for deletion
  Future<bool> _showDeleteConfirmationDialog(String entryName) async {
    // Get the current settings from ThemeService
    final themeService = Provider.of<ThemeService>(context, listen: false);
    final useSimpleConfirmation = themeService.settings.simpleDeleteConfirmation;

    if (useSimpleConfirmation) {
      // Simple confirmation with checkbox
      return _showSimpleDeleteConfirmationDialog(entryName);
    } else {
      // Advanced confirmation requiring typing the name
      return _showAdvancedDeleteConfirmationDialog(entryName);
    }
  }

  // Show a simple confirmation dialog with just a checkbox
  Future<bool> _showSimpleDeleteConfirmationDialog(String entryName) async {
    bool isConfirmed = false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Confirm Deletion'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Are you sure you want to delete "$entryName"?'),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Checkbox(
                        value: isConfirmed,
                        onChanged: (value) {
                          setState(() {
                            isConfirmed = value ?? false;
                          });
                        },
                      ),
                      const Text('Yes, I want to delete this entry'),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  style: TextButton.styleFrom(foregroundColor: Colors.red, disabledForegroundColor: Colors.grey),
                  onPressed:
                      isConfirmed
                          ? () {
                            Navigator.of(context).pop(true);
                          }
                          : null,
                  child: const Text('DELETE'),
                ),
              ],
            );
          },
        );
      },
    );

    // Return false if the dialog was dismissed
    return result ?? false;
  }

  // Show an advanced confirmation dialog that requires typing the entry name
  Future<bool> _showAdvancedDeleteConfirmationDialog(String entryName) async {
    final TextEditingController textController = TextEditingController();
    bool isNameMatch = false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Confirm Deletion'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('To confirm deletion, please type "Delete $entryName"'),
                  Text('You can simplify the confirmation in settings > security', style: TextStyle(fontSize: 14, color: Colors.grey)),
                  const SizedBox(height: 16),
                  TextField(
                    controller: textController,
                    decoration: InputDecoration(border: OutlineInputBorder(), hintText: 'Type "Delete $entryName"'),
                    onChanged: (value) {
                      setState(() {
                        isNameMatch = value == 'Delete $entryName';
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  style: TextButton.styleFrom(foregroundColor: Colors.red, disabledForegroundColor: Colors.grey),
                  onPressed:
                      isNameMatch
                          ? () {
                            Navigator.of(context).pop(true);
                          }
                          : null,
                  child: const Text('DELETE'),
                ),
              ],
            );
          },
        );
      },
    );

    // Dispose the controller
    textController.dispose();

    // Return false if the dialog was dismissed
    return result ?? false;
  }

  // Lock the app manually
  Future<void> _lockApp() async {
    _logger.d('Manually locking the app');
    try {
      // Lock the app using the auth service
      await _authService.lockApp();

      // Reload the screen to trigger authentication
      if (mounted) {
        Navigator.pushReplacementNamed(context, RouteGenerator.home);
      }
    } catch (e, stackTrace) {
      _logger.e('Error locking app', e, stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Failed to lock app')));
      }
    }
  }

  // Navigate to settings screen
  void _openSettings() async {
    _logger.d('Navigating to settings screen');
    try {
      await Navigator.pushNamed(context, RouteGenerator.settings);
      _logger.i('Returned from settings screen');
    } catch (e, stackTrace) {
      _logger.e('Error navigating to settings screen', e, stackTrace);
    }
  }

  // Toggle edit mode
  void _toggleEditMode() {
    _logger.d('Toggling edit mode: ${!_isEditMode}');
    setState(() {
      _isEditMode = !_isEditMode;
      // Clear icon cache when toggling edit mode to ensure proper rebuilding
      if (!_isEditMode) {
        _iconCache.clear();
      }
    });
  }

  // Reorder OTP entries
  void _reorderEntries(int oldIndex, int newIndex) {
    _logger.d('Reordering OTP entry from $oldIndex to $newIndex');
    setState(() {
      if (newIndex > oldIndex) {
        // When moving down, the destination index needs to be adjusted
        newIndex -= 1;
      }
      final item = _otpEntries.removeAt(oldIndex);
      _otpEntries.insert(newIndex, item);
    });
    // Save the new order
    _saveEntryOrder();
  }

  // Save the current order of OTP entries
  Future<void> _saveEntryOrder() async {
    _logger.d('Saving new OTP entry order');
    try {
      await _storageService.saveOtpEntries(_otpEntries);
      _logger.i('Successfully saved new OTP entry order');
    } catch (e, stackTrace) {
      _logger.e('Error saving OTP entry order', e, stackTrace);
    }
  }

  // Show dialog to edit OTP entry
  Future<void> _editOtpEntry(OtpEntry entry) async {
    _logger.d('Showing edit dialog for OTP entry: ${entry.name}');
    try {
      if (!mounted) return;

      final result = await _showEditOtpDialog(entry);

      if (result == true) {
        _logger.i('OTP entry edited, reloading entries');
        await _loadOtpEntries();
      } else {
        _logger.d('Edit OTP entry cancelled');
      }
    } catch (e, stackTrace) {
      _logger.e('Error editing OTP entry', e, stackTrace);
    }
  }

  // Show a dialog to edit an OTP entry
  Future<bool?> _showEditOtpDialog(OtpEntry entry) async {
    _logger.d('Building edit dialog for OTP entry: ${entry.name}');

    // Controllers for text fields
    final nameController = TextEditingController(text: entry.name);
    final issuerController = TextEditingController(text: entry.issuer);
    final iconSearchController = TextEditingController(text: entry.issuer.isNotEmpty ? entry.issuer : entry.name);

    // For icon preview
    String? selectedIconPath;
    String searchTerm = iconSearchController.text;
    bool isSearching = false;

    return showDialog<bool>(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Function to search for icons
            Future<void> searchIcons() async {
              if (searchTerm.isEmpty) return;

              setState(() {
                isSearching = true;
              });

              _logger.d('Searching for icons with term: $searchTerm');
              final iconService = Provider.of<IconService>(context, listen: false);

              // Clear the cache for this search term to ensure fresh results
              final cacheKey = '${searchTerm.toLowerCase()}:${searchTerm.toLowerCase()}';
              iconService.clearIconCache(cacheKey);

              selectedIconPath = await iconService.findIconPath(searchTerm, searchTerm);

              if (context.mounted) {
                setState(() {
                  isSearching = false;
                });
              }
            }

            // Initial icon search
            if (selectedIconPath == null && !isSearching) {
              searchIcons();
            }

            return AlertDialog(
              title: const Text('Edit OTP Entry'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name field
                    TextField(controller: nameController, decoration: const InputDecoration(labelText: 'Account Name', hintText: 'e.g., <EMAIL>')),
                    const SizedBox(height: 16),

                    // Issuer field
                    TextField(controller: issuerController, decoration: const InputDecoration(labelText: 'Issuer/Service', hintText: 'e.g., Google, GitHub')),
                    const SizedBox(height: 24),

                    // Icon search section
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Icon Search', style: TextStyle(fontWeight: FontWeight.bold)),
                        // Show reset button if entry has a custom icon search term
                        if (entry.iconSearchTerm != null)
                          TextButton.icon(
                            icon: const Icon(Icons.refresh, size: 16),
                            label: const Text('Reset to Default', style: TextStyle(fontSize: 12)),
                            onPressed: () {
                              _logger.d('DEBUG - Reset to Default button clicked');
                              _logger.d('DEBUG - Before reset - searchTerm: "$searchTerm", iconSearchController: "${iconSearchController.text}"');

                              setState(() {
                                // Reset to the issuer/name
                                iconSearchController.text = entry.issuer.isNotEmpty ? entry.issuer : entry.name;
                                searchTerm = iconSearchController.text;
                                selectedIconPath = null;
                              });

                              _logger.d('DEBUG - After reset - searchTerm: "$searchTerm", iconSearchController: "${iconSearchController.text}"');

                              // Search with the default term
                              searchIcons();
                            },
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: iconSearchController,
                            decoration: const InputDecoration(
                              labelText: 'Search Term',
                              hintText: 'e.g., github.com, google',
                              helperText: 'Enter domain or service name',
                            ),
                            onChanged: (value) {
                              searchTerm = value;
                              // Clear selected icon path when search term changes
                              if (selectedIconPath != null) {
                                setState(() {
                                  selectedIconPath = null;
                                });
                              }
                            },
                          ),
                        ),
                        IconButton(
                          icon:
                              isSearching ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2)) : const Icon(Icons.search),
                          onPressed: isSearching ? null : () => searchIcons(),
                          tooltip: 'Search for icon',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Icon preview
                    Center(
                      child: Column(
                        children: [
                          const Text('Icon Preview'),
                          const SizedBox(height: 8),
                          Container(
                            width: 64,
                            height: 64,
                            decoration: BoxDecoration(border: Border.all(color: Colors.grey), borderRadius: BorderRadius.circular(8)),
                            child:
                                isSearching
                                    ? const Center(child: CircularProgressIndicator())
                                    : selectedIconPath != null
                                    ? Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Provider.of<IconService>(
                                        context,
                                        listen: false,
                                      ).getIconWidget(issuerController.text, nameController.text, size: 48, iconSearchTerm: searchTerm),
                                    )
                                    : _buildFallbackIconPreview(searchTerm, nameController.text),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    // Validate inputs
                    if (nameController.text.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Account name cannot be empty')));
                      return;
                    }

                    // Create updated entry with the same values but potentially new icon
                    // Determine if we need to set or clear a custom icon search term
                    String? newIconSearchTerm;

                    // Check if the search term matches the issuer or name (case insensitive)
                    final issuerLower = issuerController.text.trim().toLowerCase();
                    final nameLower = nameController.text.trim().toLowerCase();
                    final searchTermLower = searchTerm.toLowerCase();

                    _logger.d('DEBUG - Current entry iconSearchTerm: ${entry.iconSearchTerm}');
                    _logger.d('DEBUG - Search term: "$searchTerm", issuer: "${issuerController.text}", name: "${nameController.text}"');
                    _logger.d('DEBUG - Lowercase comparison - searchTerm: "$searchTermLower", issuer: "$issuerLower", name: "$nameLower"');

                    // If search term matches issuer or name, clear the custom icon search term
                    if (searchTermLower == issuerLower || searchTermLower == nameLower) {
                      // Explicitly set to null to clear any existing custom icon search term
                      newIconSearchTerm = null;
                      _logger.i('Clearing custom icon search term - using default icon');
                    }
                    // Otherwise, if search term is not empty and different from issuer/name, set it as custom
                    else if (searchTerm.isNotEmpty) {
                      newIconSearchTerm = searchTerm;
                      _logger.i('Setting custom icon search term: $searchTerm');
                    }

                    _logger.d('DEBUG - New iconSearchTerm value: $newIconSearchTerm');

                    // Create the updated entry with explicit null for iconSearchTerm if needed
                    OtpEntry updatedEntry;
                    if (newIconSearchTerm == null) {
                      // Explicitly pass null to clear the iconSearchTerm
                      updatedEntry = entry.copyWith(name: nameController.text.trim(), issuer: issuerController.text.trim(), iconSearchTerm: null);
                      _logger.d('DEBUG - Explicitly setting iconSearchTerm to null');
                    } else {
                      updatedEntry = entry.copyWith(name: nameController.text.trim(), issuer: issuerController.text.trim(), iconSearchTerm: newIconSearchTerm);
                    }

                    try {
                      _logger.i('Saving updated OTP entry: ${updatedEntry.name} (${updatedEntry.issuer})');
                      _logger.d('DEBUG - Updated entry iconSearchTerm: ${updatedEntry.iconSearchTerm}');

                      // Get the icon service before the async gap
                      final iconService = Provider.of<IconService>(context, listen: false);

                      // Save the updated entry
                      await _storageService.updateOtpEntry(updatedEntry);

                      // Double-check what was saved
                      final savedEntries = await _storageService.getOtpEntries();
                      final savedEntry = savedEntries.firstWhere((e) => e.id == updatedEntry.id);
                      _logger.d('DEBUG - Saved entry iconSearchTerm: ${savedEntry.iconSearchTerm}');

                      // Clear icon cache for this entry and the search term
                      _iconCache.remove(entry.id);

                      // Also clear the icon service cache for this entry
                      final issuer = updatedEntry.issuer.toLowerCase();
                      final name = updatedEntry.name.toLowerCase();
                      iconService.clearIconCache('$issuer:$name');

                      // Log that we're using a custom icon search term if one is set
                      if (updatedEntry.iconSearchTerm != null) {
                        _logger.i('Entry will use custom icon search term: ${updatedEntry.iconSearchTerm}');
                      }

                      if (context.mounted) {
                        Navigator.of(context).pop(true);
                      }
                    } catch (e, stackTrace) {
                      _logger.e('Error saving updated OTP entry', e, stackTrace);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error saving changes: ${e.toString()}')));
                      }
                    }
                  },
                  child: const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Build a fallback icon preview for the edit dialog
  Widget _buildFallbackIconPreview(String issuer, String name) {
    // Handle empty strings to prevent RangeError
    final String displayText = (issuer.isNotEmpty ? issuer : name);
    final String firstLetter = displayText.isNotEmpty ? displayText.substring(0, 1).toUpperCase() : '?';

    return Container(
      decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(8)),
      child: Center(child: Text(firstLetter, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 24))),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get the current view type from settings
    final themeService = Provider.of<ThemeService>(context);
    final homeViewType = themeService.settings.homeViewType;

    return FScaffold(
      header: CustomAppBar(
        title: 'OpenOTP',
        actions: [
          // Lock button
          FButton.icon(onPress: _lockApp, child: const Icon(Icons.lock)),
          // View toggle button
          FButton.icon(onPress: () => _toggleViewType(themeService), child: _getViewTypeIcon(homeViewType)),
          // Edit button
          FButton.icon(onPress: _toggleEditMode, child: Icon(_isEditMode ? Icons.done : Icons.edit)),
          // Settings button
          FButton.icon(onPress: _openSettings, child: const Icon(Icons.settings)),
        ],
      ),
      child: Stack(
        children: [
          _otpEntries.isEmpty
              ? const Center(
                child: Text(
                  'No OTP entries yet. Add one to get started!\nYou should check out the settings page before adding entries.',
                  textAlign: TextAlign.center,
                ),
              )
              : _isEditMode
              // ReorderableListView for edit mode
              ? ReorderableListView.builder(
                buildDefaultDragHandles: false,
                itemCount: _otpEntries.length,
                onReorder: _reorderEntries,
                itemBuilder: (context, index) {
                  final entry = _otpEntries[index];
                  // Use cached OTP code instead of generating on every build
                  String code = _totpCache[entry.id] ?? 'Loading...';

                  // For HOTP entries, show a placeholder if no code has been generated yet
                  if (entry.type == OtpType.hotp && code == 'TAP TO GENERATE') {
                    code = 'TAP TO GENERATE';
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: FCard.raw(
                      key: Key(entry.id),
                      child: ReorderableDragStartListener(
                      index: index,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Provider icon if available
                                _buildProviderIcon(entry),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        entry.issuer.isNotEmpty ? '${entry.issuer}\n(${entry.name})' : entry.name,
                                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                        overflow: TextOverflow.visible,
                                        softWrap: true,
                                      ),
                                    ],
                                  ),
                                ),
                                // Edit button
                                Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(24),
                                    onTap: () => _editOtpEntry(entry),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Icon(Icons.edit, color: Theme.of(context).primaryColor, size: 24),
                                    ),
                                  ),
                                ),
                                // Delete button
                                Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(24),
                                    onTap: () => _deleteOtpEntry(entry.id),
                                    child: Padding(padding: const EdgeInsets.all(8.0), child: Icon(Icons.delete, color: Colors.red, size: 24)),
                                  ),
                                ),
                                // Visual indicator for drag handle
                                const SizedBox(width: 8),
                                const Icon(Icons.drag_handle, size: 18, color: Colors.grey),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(code, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, letterSpacing: 0), textAlign: TextAlign.left),
                            const SizedBox(height: 8),
                            Text(
                              'Refreshes in $_secondsRemaining seconds',
                              style: TextStyle(
                                color:
                                    _secondsRemaining < 5
                                        ? Colors
                                            .red // Keep red for warning
                                        : Theme.of(context).brightness == Brightness.dark
                                        ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                        : const Color(0xFF4F4F4F), // Light mode Text Secondary
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )
              // Choose between different view types based on settings
              : homeViewType == HomeViewType.authyStyle
              ? _buildAuthyStyleView()
              : homeViewType == HomeViewType.grid
              ? _buildGridView()
              : ListView.builder(
                itemCount: _otpEntries.length,
                itemBuilder: (context, index) {
                  final entry = _otpEntries[index];
                  // Use cached OTP code instead of generating on every build
                  String code = _totpCache[entry.id] ?? 'Loading...';

                  // For HOTP entries, show a placeholder if no code has been generated yet
                  if (entry.type == OtpType.hotp && code == 'TAP TO GENERATE') {
                    code = 'TAP TO GENERATE';
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: FCard.raw(
                      child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Provider icon if available
                              _buildProviderIcon(entry),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      entry.issuer.isNotEmpty ? '${entry.issuer}\n(${entry.name})' : entry.name,
                                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                      overflow: TextOverflow.visible,
                                      softWrap: true,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 8),
                              CircularProgressIndicator(value: _secondsRemaining / entry.period, strokeWidth: 5),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              if (code == 'ERROR')
                                Row(
                                  children: [
                                    const Icon(Icons.error, color: Colors.red),
                                    const SizedBox(width: 8),
                                    const Text('Invalid Key', style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold)),
                                    IconButton(
                                      icon: const Icon(Icons.edit, color: Colors.red),
                                      onPressed: () => _editOtpEntry(entry),
                                      tooltip: 'Edit entry to fix key',
                                    ),
                                  ],
                                )
                              else if (code == 'TAP TO GENERATE')
                                Row(
                                  children: [
                                    ElevatedButton.icon(
                                      icon: const Icon(Icons.refresh),
                                      label: const Text('Generate Code'),
                                      onPressed: () => _generateHotpCode(entry),
                                    ),
                                  ],
                                )
                              else
                                Row(
                                  children: [
                                    Text(code, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, letterSpacing: 0), textAlign: TextAlign.left),
                                    IconButton(icon: const Icon(Icons.copy), onPressed: () => _copyCodeToClipboard(code), tooltip: 'Copy code'),
                                    if (entry.type == OtpType.hotp)
                                      IconButton(icon: const Icon(Icons.refresh), onPressed: () => _generateHotpCode(entry), tooltip: 'Generate new code'),
                                  ],
                                ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          entry.type == OtpType.totp
                              ? Text(
                                'Refreshes in $_secondsRemaining seconds',
                                style: TextStyle(
                                  color:
                                      _secondsRemaining < 5
                                          ? Colors
                                              .red // Keep red for warning
                                          : Theme.of(context).brightness == Brightness.dark
                                          ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                          : const Color(0xFF4F4F4F), // Light mode Text Secondary
                                ),
                              )
                              : Text(
                                'Counter-based (HOTP)',
                                style: TextStyle(
                                  color:
                                      Theme.of(context).brightness == Brightness.dark
                                          ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                          : const Color(0xFF4F4F4F), // Light mode Text Secondary
                                ),
                              ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          // Floating Action Button
          Positioned(
            bottom: 16,
            right: 16,
            child: FButton.icon(
              onPress: () {
                _showFabMenu(context);
              },
              child: const Icon(Icons.add),
            ),
          ),
        ],
      ),
    );
  }

  // Build provider icon widget for an OTP entry
  Widget _buildProviderIcon(OtpEntry entry) {
    // Check if we already have this icon in the cache
    if (_iconCache.containsKey(entry.id)) {
      return _iconCache[entry.id]!;
    }

    _logger.d('Building provider icon for ${entry.name} (not cached)');

    // Check if we have a custom icon search term
    if (entry.iconSearchTerm != null && entry.iconSearchTerm!.isNotEmpty) {
      _logger.d('Using custom icon search term: ${entry.iconSearchTerm}');
    }

    // Get icon widget from the icon service (it will handle fallbacks internally)
    final iconWidget = _iconService.getIconWidget(
      entry.issuer,
      entry.name,
      size: 40.0,
      color:
          Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFFFFFFFF) // White for dark mode
              : null, // Original colors for light mode
      iconSearchTerm: entry.iconSearchTerm,
    );

    // Create the container with appropriate styling
    final containerWidget = Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF2A2A2A) // Slightly lighter than dark background
                : const Color(0xFFEEEEEE), // Light gray for light mode
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(4),
      child: iconWidget, // IconService now always returns a widget
    );

    // Store in cache for future use
    _iconCache[entry.id] = containerWidget;

    return containerWidget;
  }

  // Get the appropriate icon for the current view type
  Icon _getViewTypeIcon(HomeViewType viewType) {
    switch (viewType) {
      case HomeViewType.authyStyle:
        return const Icon(Icons.grid_view);
      case HomeViewType.grid:
        return const Icon(Icons.view_list);
      case HomeViewType.list:
        return const Icon(Icons.view_agenda);
    }
  }

  // Toggle between view types
  void _toggleViewType(ThemeService themeService) {
    _logger.d('Toggling view type');
    HomeViewType newViewType;

    switch (themeService.settings.homeViewType) {
      case HomeViewType.authyStyle:
        newViewType = HomeViewType.list;
        break;
      case HomeViewType.grid:
        newViewType = HomeViewType.authyStyle;
        break;
      case HomeViewType.list:
        newViewType = HomeViewType.grid;
        break;
    }

    themeService.updateHomeViewType(newViewType);
  }

  // Calculate a responsive aspect ratio that gets closer to 1.0 (square) as screen size increases
  double _calculateResponsiveAspectRatio(double screenWidth) {
    // Base aspect ratio - closer to 1.0 means more square-shaped
    // For smaller screens, we want slightly taller cards (ratio < 1.0)
    // For larger screens, we want more square cards (ratio closer to 1.0)
    if (screenWidth > 900) {
      return 1.0; // Square cards on very large screens
    } else if (screenWidth > 600) {
      return 0.95; // Almost square on medium screens
    } else {
      return 0.9; // Slightly taller than wide on small screens
    }
  }

  // Build grid view for OTP entries
  Widget _buildGridView() {
    // Calculate the number of columns based on screen width
    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = screenWidth > 900 ? 4 : (screenWidth > 600 ? 3 : 2);

    // Use a more square-shaped aspect ratio based on screen size
    final childAspectRatio = _calculateResponsiveAspectRatio(screenWidth);

    return GridView.builder(
      padding: const EdgeInsets.all(12),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount, // Responsive number of items per row
        childAspectRatio: childAspectRatio, // More square-shaped aspect ratio
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _otpEntries.length,
      itemBuilder: (context, index) {
        final entry = _otpEntries[index];
        // Use cached OTP code instead of generating on every build
        String code = _totpCache[entry.id] ?? 'Loading...';

        // For HOTP entries, show a placeholder if no code has been generated yet
        if (entry.type == OtpType.hotp && code == 'TAP TO GENERATE') {
          code = 'TAP TO GENERATE';
        }

        return FCard.raw(
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () {
              // For HOTP entries with 'TAP TO GENERATE', generate a new code
              if (entry.type == OtpType.hotp && code == 'TAP TO GENERATE') {
                _generateHotpCode(entry);
              } else {
                // Show a dialog with the full details
                _showOtpDetailsDialog(entry, code);
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Top section with icon and names
                  Expanded(
                    flex: 4,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Provider icon
                        Expanded(flex: 3, child: Center(child: _buildProviderIcon(entry))),
                        const SizedBox(height: 4),
                        // Provider name
                        Flexible(
                          child: Text(
                            entry.issuer.isNotEmpty ? entry.issuer : entry.name,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        // Account name if issuer is present
                        if (entry.issuer.isNotEmpty)
                          Flexible(
                            child: Text(
                              entry.name,
                              style: TextStyle(
                                fontSize: 11,
                                color:
                                    Theme.of(context).brightness == Brightness.dark
                                        ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                        : const Color(0xFF4F4F4F), // Light mode Text Secondary
                              ),
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Middle section with OTP code
                  Expanded(
                    flex: 3,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Token text
                          Text(
                            entry.type == OtpType.totp ? 'TOTP:' : 'HOTP:',
                            style: TextStyle(
                              fontSize: 11,
                              color:
                                  Theme.of(context).brightness == Brightness.dark
                                      ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                      : const Color(0xFF4F4F4F), // Light mode Text Secondary
                            ),
                          ),
                          const SizedBox(height: 2),
                          // OTP code
                          code == 'ERROR'
                              ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(Icons.error, color: Colors.red, size: 14),
                                  const SizedBox(width: 2),
                                  const Flexible(
                                    child: Text(
                                      'Invalid Key',
                                      style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold, fontSize: 12),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              )
                              : code == 'TAP TO GENERATE'
                              ? ElevatedButton.icon(
                                icon: const Icon(Icons.refresh, size: 14),
                                label: const Text('Generate', style: TextStyle(fontSize: 12)),
                                onPressed: () => _generateHotpCode(entry),
                                style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4)),
                              )
                              : FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(code, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold, letterSpacing: 0)),
                                    if (entry.type == OtpType.hotp)
                                      IconButton(
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(),
                                        iconSize: 16,
                                        icon: const Icon(Icons.refresh),
                                        onPressed: () => _generateHotpCode(entry),
                                        tooltip: 'Generate new code',
                                      ),
                                  ],
                                ),
                              ),
                        ],
                      ),
                    ),
                  ),

                  // Bottom section with timer and copy button
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Expiration timer
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(
                                value: _secondsRemaining / entry.period,
                                strokeWidth: 1.5,
                                color: _secondsRemaining < 5 ? Colors.red : null,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Flexible(
                              child: Text(
                                'Expires: $_secondsRemaining s',
                                style: TextStyle(
                                  fontSize: 10,
                                  color:
                                      _secondsRemaining < 5
                                          ? Colors.red
                                          : Theme.of(context).brightness == Brightness.dark
                                          ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                          : const Color(0xFF4F4F4F), // Light mode Text Secondary
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        // Copy button
                        SizedBox(
                          height: 30,
                          width: 30,
                          child: IconButton(
                            padding: EdgeInsets.zero,
                            iconSize: 18,
                            icon: const Icon(Icons.copy),
                            onPressed: () => _copyCodeToClipboard(code),
                            tooltip: 'Copy code',
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show a dialog with OTP details
  void _showOtpDetailsDialog(OtpEntry entry, String code) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                _buildProviderIcon(entry),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        entry.issuer.isNotEmpty ? entry.issuer : entry.name,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (entry.issuer.isNotEmpty)
                        Text(
                          entry.name,
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                    : const Color(0xFF4F4F4F), // Light mode Text Secondary
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  entry.type == OtpType.totp ? 'TOTP is:' : 'HOTP is:',
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                            : const Color(0xFF4F4F4F), // Light mode Text Secondary
                  ),
                ),
                const SizedBox(height: 8),
                code == 'ERROR'
                    ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error, color: Colors.red, size: 24),
                        const SizedBox(width: 8),
                        const Text('Invalid Secret Key', style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold, fontSize: 20)),
                      ],
                    )
                    : code == 'TAP TO GENERATE' && entry.type == OtpType.hotp
                    ? ElevatedButton.icon(icon: const Icon(Icons.refresh), label: const Text('Generate Code'), onPressed: () => _generateHotpCode(entry))
                    : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Flexible(
                          child: Text(code, style: const TextStyle(fontSize: 28, fontWeight: FontWeight.bold, letterSpacing: 0), textAlign: TextAlign.center),
                        ),
                        if (entry.type == OtpType.hotp)
                          IconButton(icon: const Icon(Icons.refresh), onPressed: () => _generateHotpCode(entry), tooltip: 'Generate new code'),
                      ],
                    ),
                const SizedBox(height: 16),
                Text(
                  'Refreshes in $_secondsRemaining seconds',
                  style: TextStyle(
                    color:
                        _secondsRemaining < 5
                            ? Colors.red
                            : Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                            : const Color(0xFF4F4F4F), // Light mode Text Secondary
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton.icon(
                      icon: const Icon(Icons.copy),
                      label: const Text('Copy'),
                      onPressed:
                          code == 'ERROR'
                              ? null // Disable button for invalid entries
                              : () {
                                _copyCodeToClipboard(code);
                                Navigator.pop(context);
                              },
                    ),
                    TextButton.icon(
                      icon: const Icon(Icons.edit),
                      label: const Text('Edit'),
                      onPressed: () {
                        Navigator.pop(context);
                        _editOtpEntry(entry);
                      },
                    ),
                    if (!_isEditMode)
                      TextButton.icon(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        label: const Text('Delete', style: TextStyle(color: Colors.red)),
                        onPressed: () {
                          Navigator.pop(context);
                          _deleteOtpEntry(entry.id);
                        },
                      ),
                  ],
                ),
              ],
            ),
            actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('Close'))],
          ),
    );
  }

  // Copy code to clipboard
  void _copyCodeToClipboard(String code) {
    _logger.d('Copying code to clipboard');
    Clipboard.setData(ClipboardData(text: code));
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Code copied to clipboard')));
  }

  // Generate a new HOTP code by incrementing the counter
  Future<void> _generateHotpCode(OtpEntry entry) async {
    _logger.d('Generating new HOTP code for ${entry.name}');
    try {
      // Increment the counter and generate a new code
      final updatedEntry = await _otpService.incrementHotpCounter(entry);

      // Generate the new code
      final newCode = await _otpService.generateHotp(updatedEntry);

      // Update the local entries list with the updated entry
      setState(() {
        // Find and replace the entry in the local list
        final index = _otpEntries.indexWhere((e) => e.id == entry.id);
        if (index != -1) {
          _otpEntries[index] = updatedEntry;
          _logger.d('Updated local entry with new counter: ${updatedEntry.counter}');
        }

        // Update the cache
        _totpCache[entry.id] = newCode;
      });

      _logger.i('Generated new HOTP code for ${entry.name}: $newCode');
    } catch (e, stackTrace) {
      _logger.e('Error generating HOTP code for ${entry.name}', e, stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error generating code: ${e.toString()}')));
      }
    }
  }

  // Build Authy-style view with selected TOTP at top and grid below
  Widget _buildAuthyStyleView() {
    // If no entry is selected, select the first one
    if (_selectedOtpId == null && _otpEntries.isNotEmpty) {
      _selectedOtpId = _otpEntries.first.id;
    }

    // Find the selected entry
    final selectedEntry = _otpEntries.firstWhere((entry) => entry.id == _selectedOtpId, orElse: () => _otpEntries.first);

    // Get the code for the selected entry
    String selectedCode = _totpCache[selectedEntry.id] ?? 'Loading...';

    // For HOTP entries, show a placeholder if no code has been generated yet
    if (selectedEntry.type == OtpType.hotp && selectedCode == 'TAP TO GENERATE') {
      selectedCode = 'TAP TO GENERATE';
    }

    return Column(
      children: [
        // Top section with selected TOTP
        Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Provider icon and name
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildProviderIcon(selectedEntry),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        selectedEntry.issuer.isNotEmpty ? selectedEntry.issuer : selectedEntry.name,
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      if (selectedEntry.issuer.isNotEmpty)
                        Text(
                          selectedEntry.name,
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                    : const Color(0xFF4F4F4F), // Light mode Text Secondary
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Token text
              Text(
                selectedEntry.type == OtpType.totp ? 'TOTP is:' : 'HOTP is:',
                style: TextStyle(
                  fontSize: 14,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                          : const Color(0xFF4F4F4F), // Light mode Text Secondary
                ),
              ),
              const SizedBox(height: 8),
              // OTP code
              selectedCode == 'ERROR'
                  ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, color: Colors.red, size: 24),
                      const SizedBox(width: 8),
                      const Text('Invalid Secret Key', style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold, fontSize: 24)),
                      IconButton(
                        icon: const Icon(Icons.edit, color: Colors.red),
                        onPressed: () => _editOtpEntry(selectedEntry),
                        tooltip: 'Edit entry to fix key',
                      ),
                    ],
                  )
                  : selectedCode == 'TAP TO GENERATE'
                  ? ElevatedButton.icon(
                    icon: const Icon(Icons.refresh),
                    label: const Text('Generate Code'),
                    onPressed: () => _generateHotpCode(selectedEntry),
                    style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
                  )
                  : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(selectedCode, style: const TextStyle(fontSize: 36, fontWeight: FontWeight.bold, letterSpacing: 0), textAlign: TextAlign.center),
                      if (selectedEntry.type == OtpType.hotp)
                        IconButton(icon: const Icon(Icons.refresh, size: 28), onPressed: () => _generateHotpCode(selectedEntry), tooltip: 'Generate new code'),
                    ],
                  ),
              const SizedBox(height: 8),
              // Timer or counter info
              selectedEntry.type == OtpType.totp
                  ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(width: 30, height: 30, child: CircularProgressIndicator(value: _secondsRemaining / selectedEntry.period, strokeWidth: 3)),
                      const SizedBox(width: 8),
                      Text(
                        'Refreshes in $_secondsRemaining seconds',
                        style: TextStyle(
                          color:
                              _secondsRemaining < 5
                                  ? Colors.red
                                  : Theme.of(context).brightness == Brightness.dark
                                  ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                  : const Color(0xFF4F4F4F), // Light mode Text Secondary
                        ),
                      ),
                    ],
                  )
                  : Text(
                    'Counter: ${selectedEntry.counter}',
                    style: TextStyle(
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                              : const Color(0xFF4F4F4F), // Light mode Text Secondary
                    ),
                  ),
              const SizedBox(height: 16),
              // Copy button
              ElevatedButton.icon(
                icon: const Icon(Icons.copy),
                label: const Text('Copy'),
                onPressed: selectedCode == 'ERROR' ? null : () => _copyCodeToClipboard(selectedCode),
              ),
            ],
          ),
        ),
        // Divider
        const Divider(height: 1),
        // Bottom section with grid of TOTPs
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.all(12),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              // Responsive grid based on screen width
              crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 3,
              // Use the same square-shaped aspect ratio as the main grid view
              childAspectRatio: _calculateResponsiveAspectRatio(MediaQuery.of(context).size.width),
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _otpEntries.length,
            itemBuilder: (context, index) {
              final entry = _otpEntries[index];
              final isSelected = entry.id == _selectedOtpId;

              return FCard.raw(
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    setState(() {
                      _selectedOtpId = entry.id;
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Provider icon
                        Expanded(flex: 6, child: Center(child: _buildProviderIcon(entry))),
                        // Provider name and account
                        Expanded(
                          flex: 3,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Provider name
                              Flexible(
                                child: Text(
                                  entry.issuer.isNotEmpty ? entry.issuer : entry.name,
                                  style: TextStyle(fontWeight: isSelected ? FontWeight.bold : FontWeight.normal, fontSize: 12),
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              // Account name if issuer is present
                              if (entry.issuer.isNotEmpty)
                                Flexible(
                                  child: Text(
                                    entry.name,
                                    style: TextStyle(
                                      fontSize: 10,
                                      color:
                                          Theme.of(context).brightness == Brightness.dark
                                              ? const Color(0xFFB0B0B0) // Dark mode Text Secondary
                                              : const Color(0xFF4F4F4F), // Light mode Text Secondary
                                    ),
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showFabMenu(BuildContext context) {
    _logger.d('Showing FAB menu');
    showFSheet(
      context: context,
      side: FLayout.btt,
      builder:
          (context) => Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                FTile(
                  prefix: const Icon(Icons.qr_code_scanner),
                  title: const Text('Scan QR Code'),
                  onPress: () {
                    Navigator.pop(context);
                    _scanQrCode();
                  },
                ),
                FTile(
                  prefix: const Icon(Icons.image),
                  title: const Text('Scan QR from Image'),
                  onPress: () {
                    Navigator.pop(context);
                    _scanQrFromImage();
                  },
                ),
                FTile(
                  prefix: const Icon(Icons.keyboard),
                  title: const Text('Manual Entry'),
                  onPress: () {
                    Navigator.pop(context);
                    _addOtpEntry();
                  },
                ),
              ],
            ),
          ),
    );
  }
}
