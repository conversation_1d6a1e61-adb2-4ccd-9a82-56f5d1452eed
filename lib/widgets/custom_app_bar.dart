import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class CustomAppBar extends StatelessWidget {
  final String title;
  final List<Widget>? actions;
  final bool primary;

  const CustomAppBar({super.key, required this.title, this.actions, this.primary = true});

  @override
  Widget build(BuildContext context) {
    final isRootRoute = ModalRoute.of(context)?.isFirst ?? true;

    // Convert actions to FHeaderAction widgets
    final List<FHeaderAction> headerActions =
        actions?.map((widget) {
          if (widget is IconButton) {
            return FHeaderAction(icon: widget.icon, onPress: widget.onPressed);
          } else if (widget is FButton) {
            // Extract icon and onPress from FButton
            return FHeaderAction(icon: widget.child, onPress: widget.onPress);
          }
          // For other widget types, wrap in a custom action
          return FHeaderAction(icon: widget, onPress: () {});
        }).toList() ??
        [];

    if (isRootRoute) {
      return FHeader(title: Text(title), suffixes: headerActions);
    } else {
      return FHeader.nested(title: Text(title), prefixes: [FHeaderAction.back(onPress: () => Navigator.of(context).maybePop())], suffixes: headerActions);
    }
  }
}
