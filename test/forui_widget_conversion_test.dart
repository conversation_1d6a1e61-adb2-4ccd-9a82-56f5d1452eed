import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:forui/forui.dart';
import 'package:openotp/widgets/custom_app_bar.dart';

void main() {
  group('Forui Widget Conversion Tests', () {
    testWidgets('CustomAppBar should use FHeader', (WidgetTester tester) async {
      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              appBar: CustomAppBar(title: 'Test Title'),
            ),
          ),
        ),
      );

      // Verify that FHeader is used
      expect(find.byType(FHeader), findsOneWidget);
      expect(find.text('Test Title'), findsOneWidget);
    });

    testWidgets('CustomAppBar should handle actions correctly', (WidgetTester tester) async {
      bool actionPressed = false;

      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              appBar: CustomAppBar(
                title: 'Test Title',
                actions: [
                  FButton.icon(
                    onPress: () => actionPressed = true,
                    child: const Icon(Icons.settings),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Verify that the action button is present
      expect(find.byIcon(Icons.settings), findsOneWidget);

      // Tap the action button
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pump();

      expect(actionPressed, isTrue);
    });

    testWidgets('FButton should render correctly', (WidgetTester tester) async {
      bool buttonPressed = false;

      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: FButton(
                onPress: () => buttonPressed = true,
                child: const Text('Test Button'),
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FButton), findsOneWidget);
      expect(find.text('Test Button'), findsOneWidget);

      await tester.tap(find.byType(FButton));
      await tester.pump();

      expect(buttonPressed, isTrue);
    });

    testWidgets('FButton.icon should render correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: FButton.icon(
                onPress: () {},
                child: const Icon(Icons.add),
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FButton), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('FTile should render correctly', (WidgetTester tester) async {
      bool tilePressed = false;

      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: FTile(
                title: const Text('Test Tile'),
                subtitle: const Text('Test Subtitle'),
                prefix: const Icon(Icons.star),
                onPress: () => tilePressed = true,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FTile), findsOneWidget);
      expect(find.text('Test Tile'), findsOneWidget);
      expect(find.text('Test Subtitle'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);

      await tester.tap(find.byType(FTile));
      await tester.pump();

      expect(tilePressed, isTrue);
    });

    testWidgets('FSwitch should render and toggle correctly', (WidgetTester tester) async {
      bool switchValue = false;

      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: StatefulBuilder(
                builder: (context, setState) {
                  return FSwitch(
                    value: switchValue,
                    onChange: (value) {
                      setState(() {
                        switchValue = value;
                      });
                    },
                  );
                },
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FSwitch), findsOneWidget);

      // Initially should be false
      expect(switchValue, isFalse);

      // Tap to toggle
      await tester.tap(find.byType(FSwitch));
      await tester.pump();

      expect(switchValue, isTrue);
    });

    testWidgets('FCheckbox should render and toggle correctly', (WidgetTester tester) async {
      bool checkboxValue = false;

      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: StatefulBuilder(
                builder: (context, setState) {
                  return FCheckbox(
                    value: checkboxValue,
                    onChange: (value) {
                      setState(() {
                        checkboxValue = value;
                      });
                    },
                  );
                },
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FCheckbox), findsOneWidget);

      // Initially should be false
      expect(checkboxValue, isFalse);

      // Tap to toggle
      await tester.tap(find.byType(FCheckbox));
      await tester.pump();

      expect(checkboxValue, isTrue);
    });

    testWidgets('FAccordion should render and expand correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: FAccordion(
                controller: FAccordionController(),
                children: [
                  FAccordionItem(
                    title: const Text('Test Accordion'),
                    child: const Text('Accordion Content'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FAccordion), findsOneWidget);
      expect(find.text('Test Accordion'), findsOneWidget);

      // Content should not be visible initially
      expect(find.text('Accordion Content'), findsNothing);

      // Tap to expand
      await tester.tap(find.text('Test Accordion'));
      await tester.pumpAndSettle();

      // Content should now be visible
      expect(find.text('Accordion Content'), findsOneWidget);
    });

    testWidgets('FTextFormField should render correctly', (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: FTextFormField(
                controller: controller,
                label: const Text('Test Label'),
                hint: 'Test Hint',
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FTextFormField), findsOneWidget);
      expect(find.text('Test Label'), findsOneWidget);

      // Enter text
      await tester.enterText(find.byType(FTextFormField), 'Test Input');
      expect(controller.text, equals('Test Input'));
    });

    testWidgets('FCard should render correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: FCard(
                title: 'Test Card',
                subtitle: 'Test Subtitle',
                child: const Text('Card Content'),
              ),
            ),
          ),
        ),
      );

      expect(find.byType(FCard), findsOneWidget);
      expect(find.text('Test Card'), findsOneWidget);
      expect(find.text('Test Subtitle'), findsOneWidget);
      expect(find.text('Card Content'), findsOneWidget);
    });

    testWidgets('FScaffold should render correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: FScaffold(
              header: CustomAppBar(title: 'Test'),
              child: const Text('Scaffold Content'),
            ),
          ),
        ),
      );

      expect(find.byType(FScaffold), findsOneWidget);
      expect(find.text('Test'), findsOneWidget);
      expect(find.text('Scaffold Content'), findsOneWidget);
    });
  });
}
