import 'package:flutter_test/flutter_test.dart';

// Import all forui migration test files
import 'theme_service_forui_test.dart' as theme_service_tests;
import 'forui_widget_conversion_test.dart' as widget_conversion_tests;
import 'icon_standardization_test.dart' as icon_tests;
import 'forui_migration_integration_test.dart' as integration_tests;

void main() {
  group('Forui Migration Test Suite', () {
    group('Theme Service Forui Integration', () {
      theme_service_tests.main();
    });

    group('Widget Conversion Tests', () {
      widget_conversion_tests.main();
    });

    group('Icon Standardization Tests', () {
      icon_tests.main();
    });

    group('Integration Tests', () {
      integration_tests.main();
    });
  });
}
