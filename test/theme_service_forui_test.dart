import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:forui/forui.dart';
import 'package:openotp/services/theme_service.dart';
import 'package:openotp/models/settings_model.dart';
import 'package:openotp/services/settings_service_interface.dart';
import 'package:openotp/services/logger_service.dart';
import 'package:openotp/services/auth_service.dart';

// Mock settings service for testing
class MockSettingsService implements ISettingsService {
  SettingsModel _settings = const SettingsModel();

  @override
  Future<SettingsModel> loadSettings() async => _settings;

  @override
  Future<SettingsModel> updateThemeMode(ThemeMode themeMode) async {
    _settings = _settings.copyWith(themeMode: themeMode);
    return _settings;
  }

  @override
  Future<SettingsModel> updateThemeStyleType(ThemeStyleType themeStyleType) async {
    _settings = _settings.copyWith(themeStyleType: themeStyleType);
    return _settings;
  }

  @override
  Future<SettingsModel> updateCustomLightTheme(CustomThemeModel theme) async {
    _settings = _settings.copyWith(lightCustomTheme: theme);
    return _settings;
  }

  @override
  Future<SettingsModel> updateCustomDarkTheme(CustomThemeModel theme) async {
    _settings = _settings.copyWith(darkCustomTheme: theme);
    return _settings;
  }

  // Other required methods (simplified for testing)
  @override
  Future<SettingsModel> updateUseBiometrics(bool useBiometrics) async {
    _settings = _settings.copyWith(useBiometrics: useBiometrics);
    return _settings;
  }

  @override
  Future<SettingsModel> updateAutoLockTimeout(int timeout) async {
    _settings = _settings.copyWith(autoLockTimeout: timeout);
    return _settings;
  }

  @override
  Future<SettingsModel> updatePageTransitionType(PageTransitionType type) async {
    _settings = _settings.copyWith(pageTransitionType: type);
    return _settings;
  }

  @override
  Future<SettingsModel> updateHomeViewType(HomeViewType type) async {
    _settings = _settings.copyWith(homeViewType: type);
    return _settings;
  }

  @override
  Future<SettingsModel> updateSimpleDeleteConfirmation(bool simple) async {
    _settings = _settings.copyWith(simpleDeleteConfirmation: simple);
    return _settings;
  }

  @override
  Future<SettingsModel> updateDeviceName(String name) async {
    _settings = _settings.copyWith(deviceName: name);
    return _settings;
  }

  @override
  Future<SettingsModel> updateSyncPin(String? pin) async {
    _settings = _settings.copyWith(syncPin: pin);
    return _settings;
  }

  @override
  Future<SettingsModel> updateServerPort(int? port) async {
    _settings = _settings.copyWith(serverPort: port);
    return _settings;
  }

  @override
  Future<SettingsModel> updateClientPort(int? port) async {
    _settings = _settings.copyWith(clientPort: port);
    return _settings;
  }

  @override
  Future<SettingsModel> updateUsePasswordEncryption(bool use) async {
    _settings = _settings.copyWith(usePasswordEncryption: use);
    return _settings;
  }
}

// Mock logger service
class MockLoggerService extends LoggerService {
  @override
  void d(String message) {}
  
  @override
  void i(String message) {}
  
  @override
  void e(String message, [Object? error, StackTrace? stackTrace]) {}
}

// Mock auth service
class MockAuthService extends AuthService {}

void main() {
  group('ThemeService Forui Integration Tests', () {
    late ThemeService themeService;
    late MockSettingsService mockSettingsService;

    setUp(() {
      mockSettingsService = MockSettingsService();
      themeService = ThemeService(
        settingsService: mockSettingsService,
        logger: MockLoggerService(),
        authService: MockAuthService(),
      );
    });

    test('should return default blue forui theme for default style', () async {
      await themeService.initialize();
      
      final foruiTheme = themeService.foruiTheme;
      
      expect(foruiTheme, isA<FThemeData>());
      expect(foruiTheme.colors.brightness, equals(Brightness.light));
      expect(foruiTheme.colors.primary, equals(FThemes.blue.light.colors.primary));
    });

    test('should return green forui theme for forest style', () async {
      await themeService.initialize();
      await themeService.updateThemeStyleType(ThemeStyleType.forest);
      
      final foruiTheme = themeService.foruiTheme;
      
      expect(foruiTheme.colors.primary, equals(FThemes.green.light.colors.primary));
    });

    test('should return orange forui theme for sunset style', () async {
      await themeService.initialize();
      await themeService.updateThemeStyleType(ThemeStyleType.sunset);
      
      final foruiTheme = themeService.foruiTheme;
      
      expect(foruiTheme.colors.primary, equals(FThemes.orange.light.colors.primary));
    });

    test('should return violet forui theme for violet style', () async {
      await themeService.initialize();
      await themeService.updateThemeStyleType(ThemeStyleType.violet);
      
      final foruiTheme = themeService.foruiTheme;
      
      expect(foruiTheme.colors.primary, equals(FThemes.violet.light.colors.primary));
    });

    test('should return dark theme when theme mode is dark', () async {
      await themeService.initialize();
      await themeService.updateThemeMode(ThemeMode.dark);
      
      final foruiTheme = themeService.foruiTheme;
      
      expect(foruiTheme.colors.brightness, equals(Brightness.dark));
      expect(foruiTheme.colors.primary, equals(FThemes.blue.dark.colors.primary));
    });

    test('should create custom forui theme from custom theme model', () async {
      await themeService.initialize();
      
      const customTheme = CustomThemeModel(
        primaryColor: Color(0xFFFF5722),
        scaffoldBackgroundColor: Color(0xFFF5F5F5),
        textPrimaryColor: Color(0xFF212121),
      );
      
      await themeService.updateThemeStyleType(ThemeStyleType.custom);
      await themeService.updateCustomLightTheme(customTheme);
      
      final foruiTheme = themeService.foruiTheme;
      
      expect(foruiTheme.colors.primary, equals(const Color(0xFFFF5722)));
      expect(foruiTheme.colors.background, equals(const Color(0xFFF5F5F5)));
      expect(foruiTheme.colors.foreground, equals(const Color(0xFF212121)));
    });

    test('should handle null custom theme gracefully', () async {
      await themeService.initialize();
      await themeService.updateThemeStyleType(ThemeStyleType.custom);
      
      final foruiTheme = themeService.foruiTheme;
      
      // Should fallback to base zinc theme
      expect(foruiTheme.colors.primary, equals(FThemes.zinc.light.colors.primary));
    });

    test('should preserve typography and style from base theme', () async {
      await themeService.initialize();
      
      const customTheme = CustomThemeModel(primaryColor: Color(0xFFFF5722));
      await themeService.updateThemeStyleType(ThemeStyleType.custom);
      await themeService.updateCustomLightTheme(customTheme);
      
      final foruiTheme = themeService.foruiTheme;
      final baseTheme = FThemes.zinc.light;
      
      expect(foruiTheme.typography, equals(baseTheme.typography));
      expect(foruiTheme.style, equals(baseTheme.style));
    });
  });
}
