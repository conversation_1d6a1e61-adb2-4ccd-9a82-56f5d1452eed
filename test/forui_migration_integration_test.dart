import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:forui/forui.dart';
import 'package:openotp/main.dart' as app;
import 'package:openotp/services/theme_service.dart';
import 'package:openotp/models/settings_model.dart';
import 'package:provider/provider.dart';

void main() {
  group('Forui Migration Integration Tests', () {
    testWidgets('App should start with forui theme', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Verify that FTheme is at the root
      expect(find.byType(FTheme), findsOneWidget);
      
      // Get the FTheme widget
      final fTheme = tester.widget<FTheme>(find.byType(FTheme));
      expect(fTheme.data, isA<FThemeData>());
    });

    testWidgets('Theme changes should update forui theme', (WidgetTester tester) async {
      final themeService = ThemeService();
      await themeService.initialize();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeService,
          child: Consumer<ThemeService>(
            builder: (context, service, child) {
              return FTheme(
                data: service.foruiTheme,
                child: MaterialApp(
                  home: Scaffold(
                    body: Container(),
                  ),
                ),
              );
            },
          ),
        ),
      );

      // Get initial theme
      final initialTheme = tester.widget<FTheme>(find.byType(FTheme)).data;
      final initialPrimary = initialTheme.colors.primary;

      // Change theme style
      await themeService.updateThemeStyleType(ThemeStyleType.forest);
      await tester.pump();

      // Get updated theme
      final updatedTheme = tester.widget<FTheme>(find.byType(FTheme)).data;
      final updatedPrimary = updatedTheme.colors.primary;

      // Verify theme changed
      expect(updatedPrimary, isNot(equals(initialPrimary)));
      expect(updatedPrimary, equals(FThemes.green.light.colors.primary));
    });

    testWidgets('Dark mode should work with forui themes', (WidgetTester tester) async {
      final themeService = ThemeService();
      await themeService.initialize();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeService,
          child: Consumer<ThemeService>(
            builder: (context, service, child) {
              return FTheme(
                data: service.foruiTheme,
                child: MaterialApp(
                  home: Scaffold(
                    body: Container(),
                  ),
                ),
              );
            },
          ),
        ),
      );

      // Initially should be light mode
      final lightTheme = tester.widget<FTheme>(find.byType(FTheme)).data;
      expect(lightTheme.colors.brightness, equals(Brightness.light));

      // Switch to dark mode
      await themeService.updateThemeMode(ThemeMode.dark);
      await tester.pump();

      // Should now be dark mode
      final darkTheme = tester.widget<FTheme>(find.byType(FTheme)).data;
      expect(darkTheme.colors.brightness, equals(Brightness.dark));
    });

    testWidgets('Custom themes should work with forui', (WidgetTester tester) async {
      final themeService = ThemeService();
      await themeService.initialize();

      const customTheme = CustomThemeModel(
        primaryColor: Color(0xFFE91E63),
        scaffoldBackgroundColor: Color(0xFFFCE4EC),
        textPrimaryColor: Color(0xFF880E4F),
      );

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeService,
          child: Consumer<ThemeService>(
            builder: (context, service, child) {
              return FTheme(
                data: service.foruiTheme,
                child: MaterialApp(
                  home: Scaffold(
                    body: Container(),
                  ),
                ),
              );
            },
          ),
        ),
      );

      // Set custom theme
      await themeService.updateThemeStyleType(ThemeStyleType.custom);
      await themeService.updateCustomLightTheme(customTheme);
      await tester.pump();

      // Verify custom colors are applied
      final foruiTheme = tester.widget<FTheme>(find.byType(FTheme)).data;
      expect(foruiTheme.colors.primary, equals(const Color(0xFFE91E63)));
      expect(foruiTheme.colors.background, equals(const Color(0xFFFCE4EC)));
      expect(foruiTheme.colors.foreground, equals(const Color(0xFF880E4F)));
    });

    testWidgets('All theme styles should have valid forui themes', (WidgetTester tester) async {
      final themeService = ThemeService();
      await themeService.initialize();

      final themeStyles = [
        ThemeStyleType.defaultStyle,
        ThemeStyleType.forest,
        ThemeStyleType.sunset,
        ThemeStyleType.violet,
        ThemeStyleType.custom,
      ];

      for (final style in themeStyles) {
        await themeService.updateThemeStyleType(style);
        
        final foruiTheme = themeService.foruiTheme;
        
        // Verify theme is valid
        expect(foruiTheme, isA<FThemeData>());
        expect(foruiTheme.colors, isA<FColors>());
        expect(foruiTheme.typography, isA<FTypography>());
        expect(foruiTheme.style, isA<FStyle>());
        
        // Verify colors are not null
        expect(foruiTheme.colors.primary, isA<Color>());
        expect(foruiTheme.colors.background, isA<Color>());
        expect(foruiTheme.colors.foreground, isA<Color>());
      }
    });

    testWidgets('Forui widgets should render without errors', (WidgetTester tester) async {
      await tester.pumpWidget(
        FTheme(
          data: FThemes.zinc.light,
          child: MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  FButton(
                    onPress: () {},
                    child: const Text('Test Button'),
                  ),
                  FTile(
                    title: const Text('Test Tile'),
                    onPress: () {},
                  ),
                  FCard(
                    title: 'Test Card',
                    child: const Text('Card content'),
                  ),
                  FSwitch(
                    value: false,
                    onChange: (value) {},
                  ),
                  FCheckbox(
                    value: false,
                    onChange: (value) {},
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Verify all widgets render without errors
      expect(find.byType(FButton), findsOneWidget);
      expect(find.byType(FTile), findsOneWidget);
      expect(find.byType(FCard), findsOneWidget);
      expect(find.byType(FSwitch), findsOneWidget);
      expect(find.byType(FCheckbox), findsOneWidget);
      
      // Verify text content is displayed
      expect(find.text('Test Button'), findsOneWidget);
      expect(find.text('Test Tile'), findsOneWidget);
      expect(find.text('Test Card'), findsOneWidget);
      expect(find.text('Card content'), findsOneWidget);
    });

    testWidgets('Theme transitions should be smooth', (WidgetTester tester) async {
      final themeService = ThemeService();
      await themeService.initialize();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeService,
          child: Consumer<ThemeService>(
            builder: (context, service, child) {
              return FTheme(
                data: service.foruiTheme,
                child: MaterialApp(
                  home: Scaffold(
                    body: FButton(
                      onPress: () {},
                      child: const Text('Test Button'),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );

      // Verify initial state
      expect(find.byType(FButton), findsOneWidget);

      // Change theme multiple times rapidly
      await themeService.updateThemeStyleType(ThemeStyleType.forest);
      await tester.pump();
      expect(find.byType(FButton), findsOneWidget);

      await themeService.updateThemeStyleType(ThemeStyleType.sunset);
      await tester.pump();
      expect(find.byType(FButton), findsOneWidget);

      await themeService.updateThemeStyleType(ThemeStyleType.violet);
      await tester.pump();
      expect(find.byType(FButton), findsOneWidget);

      // Button should still be functional after theme changes
      await tester.tap(find.byType(FButton));
      await tester.pump();
      expect(find.byType(FButton), findsOneWidget);
    });

    test('Forui theme data should be serializable', () {
      final themeService = ThemeService();
      final foruiTheme = themeService.foruiTheme;
      
      // Verify that the theme data has all required properties
      expect(foruiTheme.colors, isA<FColors>());
      expect(foruiTheme.typography, isA<FTypography>());
      expect(foruiTheme.style, isA<FStyle>());
      
      // Verify colors have valid values
      expect(foruiTheme.colors.primary.value, isA<int>());
      expect(foruiTheme.colors.background.value, isA<int>());
      expect(foruiTheme.colors.foreground.value, isA<int>());
    });
  });
}
