import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:forui/forui.dart';
import 'package:openotp/screens/home_screen.dart';
import 'package:openotp/screens/settings_screen.dart';
import 'package:openotp/services/theme_service.dart';
import 'package:openotp/services/icon_service.dart';
import 'package:openotp/services/secure_storage_service.dart';
import 'package:provider/provider.dart';

void main() {
  group('Icon Standardization Tests', () {
    testWidgets('HOTP generation should use refresh icon instead of casino', (WidgetTester tester) async {
      // This test verifies that HOTP code generation uses the refresh icon
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeService()),
            Provider<IconService>(create: (_) => IconService()),
          ],
          child: FTheme(
            data: FThemes.zinc.light,
            child: MaterialApp(
              home: const HomeScreen(),
            ),
          ),
        ),
      );

      // Look for refresh icons (should be present for HOTP generation)
      expect(find.byIcon(Icons.refresh), findsWidgets);
      
      // Ensure casino icons are not used
      expect(find.byIcon(Icons.casino), findsNothing);
    });

    testWidgets('View toggle icons should be intuitive', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeService()),
            Provider<IconService>(create: (_) => IconService()),
          ],
          child: FTheme(
            data: FThemes.zinc.light,
            child: MaterialApp(
              home: const HomeScreen(),
            ),
          ),
        ),
      );

      // Check that appropriate view icons are used
      final viewIcons = [
        Icons.grid_view,
        Icons.view_list,
        Icons.view_agenda,
      ];

      bool foundViewIcon = false;
      for (final icon in viewIcons) {
        if (tester.widget<Icon?>(find.byIcon(icon).first) != null) {
          foundViewIcon = true;
          break;
        }
      }
      
      expect(foundViewIcon, isTrue, reason: 'Should use appropriate view toggle icons');
    });

    testWidgets('Manual entry should use keyboard icon', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeService()),
            Provider<IconService>(create: (_) => IconService()),
          ],
          child: FTheme(
            data: FThemes.zinc.light,
            child: MaterialApp(
              home: const HomeScreen(),
            ),
          ),
        ),
      );

      // Tap the FAB to show the menu
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Look for keyboard icon for manual entry
      expect(find.byIcon(Icons.keyboard), findsOneWidget);
    });

    testWidgets('Settings screen should use appropriate icons', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeService()),
          ],
          child: FTheme(
            data: FThemes.zinc.light,
            child: MaterialApp(
              home: const SettingsScreen(),
            ),
          ),
        ),
      );

      // Check for appropriate section icons
      expect(find.byIcon(Icons.palette), findsWidgets); // Appearance
      expect(find.byIcon(Icons.security), findsOneWidget); // Security
      expect(find.byIcon(Icons.storage), findsOneWidget); // Data Management
      expect(find.byIcon(Icons.sync), findsWidgets); // Synchronization
      expect(find.byIcon(Icons.info_outline), findsWidgets); // About
    });

    testWidgets('Password-related icons should be consistent', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeService()),
          ],
          child: FTheme(
            data: FThemes.zinc.light,
            child: MaterialApp(
              home: const SettingsScreen(),
            ),
          ),
        ),
      );

      // App password should use lock icon
      expect(find.byIcon(Icons.lock), findsWidgets);
      
      // Should not use the old password icon
      expect(find.byIcon(Icons.password), findsNothing);
    });

    testWidgets('File operation icons should be meaningful', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeService()),
          ],
          child: FTheme(
            data: FThemes.zinc.light,
            child: MaterialApp(
              home: const SettingsScreen(),
            ),
          ),
        ),
      );

      // Export should use upload icon
      expect(find.byIcon(Icons.upload_file), findsOneWidget);
      
      // Import should use download icon
      expect(find.byIcon(Icons.download_rounded), findsOneWidget);
      
      // Licenses should use description icon instead of source
      expect(find.byIcon(Icons.description), findsOneWidget);
      expect(find.byIcon(Icons.source), findsNothing);
    });

    test('Icon choices should be semantically correct', () {
      // Test that icon choices make semantic sense
      const iconMappings = {
        'HOTP generation': Icons.refresh, // Refresh makes sense for generating new codes
        'Manual entry': Icons.keyboard, // Keyboard makes sense for manual input
        'App password': Icons.lock, // Lock makes sense for password/security
        'Export data': Icons.upload_file, // Upload makes sense for exporting
        'Import data': Icons.download_rounded, // Download makes sense for importing
        'View toggle': Icons.grid_view, // Grid view makes sense for layout toggle
        'QR scanner': Icons.qr_code_scanner, // QR scanner is perfect for QR scanning
        'Image picker': Icons.image, // Image icon makes sense for image selection
        'Settings': Icons.settings, // Settings gear is universal
        'Edit': Icons.edit, // Edit pencil is universal
        'Delete': Icons.delete, // Delete trash can is universal
        'Copy': Icons.copy, // Copy icon is clear
        'Add': Icons.add, // Plus icon is universal for adding
      };

      // Verify that each mapping makes semantic sense
      for (final entry in iconMappings.entries) {
        expect(entry.value, isA<IconData>(), reason: '${entry.key} should have a valid icon');
      }
    });

    test('Deprecated icons should not be used', () {
      // List of icons that should not be used anymore
      const deprecatedIcons = [
        Icons.casino, // Replaced with Icons.refresh for HOTP generation
        Icons.password, // Replaced with Icons.lock for password settings
        Icons.source, // Replaced with Icons.description for licenses
        Icons.view_list, // Replaced with more intuitive view icons
        Icons.dashboard_customize, // Replaced with more intuitive view icons
        Icons.view_module, // Replaced with more intuitive view icons
      ];

      // This is more of a documentation test to ensure we remember not to use these
      for (final icon in deprecatedIcons) {
        expect(icon, isA<IconData>(), reason: 'Icon exists but should not be used in the app');
      }
    });

    testWidgets('Icon consistency across similar actions', (WidgetTester tester) async {
      // Test that similar actions use consistent icons across the app
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ThemeService()),
            Provider<IconService>(create: (_) => IconService()),
          ],
          child: FTheme(
            data: FThemes.zinc.light,
            child: MaterialApp(
              home: const HomeScreen(),
            ),
          ),
        ),
      );

      // All edit actions should use the same edit icon
      final editIcons = find.byIcon(Icons.edit);
      if (editIcons.evaluate().isNotEmpty) {
        // If edit icons are found, they should all be the same
        expect(editIcons, findsWidgets);
      }

      // All delete actions should use the same delete icon
      final deleteIcons = find.byIcon(Icons.delete);
      if (deleteIcons.evaluate().isNotEmpty) {
        expect(deleteIcons, findsWidgets);
      }

      // All copy actions should use the same copy icon
      final copyIcons = find.byIcon(Icons.copy);
      if (copyIcons.evaluate().isNotEmpty) {
        expect(copyIcons, findsWidgets);
      }
    });
  });
}
