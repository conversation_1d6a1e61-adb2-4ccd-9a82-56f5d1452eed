name: Mirror to GitLab

on:
  workflow_dispatch:

jobs:
  mirror:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Mirror to GitLab
        env:
          GITLAB_TOKEN: ${{ secrets.GITLAB_TOKEN }}
        run: |
          git remote remove gitlab 2>/dev/null || true
          git remote add gitlab "https://oauth2:${GITLAB_TOKEN}@gitlab.com/Slipstreamm/openotp.git"
          git push gitlab --all --force  # Push all branches
          git push gitlab --tags --force # Push all tags

