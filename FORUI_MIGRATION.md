# Forui Migration Documentation

This document outlines the migration from Material Design widgets to Forui widgets in the OpenOTP application.

## Overview

The migration to Forui provides a more modern, consistent, and customizable UI framework while maintaining the app's functionality and user experience.

## Migration Summary

### 1. Widget Conversions

#### Core Widgets
- `AppBar` → `FHeader` (via CustomAppBar)
- `Scaffold` → `FScaffold`
- `Card` → `FCard`
- `ElevatedButton` → `FButton`
- `IconButton` → `FButton.icon`
- `TextButton` → `FButton` with outline style

#### Form Widgets
- `TextField` → `FTextField`
- `TextFormField` → `FTextFormField`
- `Switch` → `FSwitch`
- `Checkbox` → `FCheckbox`

#### List Widgets
- `ListTile` → `FTile`
- `SwitchListTile` → `FTile` with `FSwitch` suffix
- `CheckboxListTile` → `FTile` with `FCheckbox` suffix
- `RadioListTile` → Kept as-is (complex dialog usage)

#### Layout Widgets
- `ExpansionTile` → `FAccordion` with `FAccordionItem`

### 2. Theme Integration

#### Forui Theme Support
- Enhanced `ThemeService` to support `FThemeData`
- Added `foruiTheme` getter that maps theme styles to Forui themes:
  - Default → Blue theme
  - Forest → Green theme
  - Sunset → Orange theme
  - Violet → Violet theme
  - Custom → Custom FThemeData based on CustomThemeModel

#### Theme Mapping
- Material themes are converted to Forui themes
- Custom themes are properly mapped to Forui color schemes
- Dark/light mode support maintained

### 3. Icon Standardization

#### Improved Icons
- `Icons.casino` → `Icons.refresh` (for HOTP generation)
- `Icons.password` → `Icons.lock` (for app password)
- `Icons.source` → `Icons.description` (for licenses)
- `Icons.edit` → `Icons.keyboard` (for manual entry)
- Improved view toggle icons for better UX

#### Icon Consistency
- Standardized icons across similar actions
- Removed confusing or inappropriate icon choices
- Ensured semantic correctness of icon usage

## Testing

### Test Coverage

#### 1. Theme Service Tests (`theme_service_forui_test.dart`)
- Forui theme generation for all theme styles
- Dark/light mode switching
- Custom theme integration
- Null safety handling

#### 2. Widget Conversion Tests (`forui_widget_conversion_test.dart`)
- All converted widgets render correctly
- Widget interactions work as expected
- Form validation and state management
- Event handling and callbacks

#### 3. Icon Standardization Tests (`icon_standardization_test.dart`)
- Correct icon usage throughout the app
- Deprecated icons are not used
- Icon consistency across similar actions
- Semantic correctness of icon choices

#### 4. Integration Tests (`forui_migration_integration_test.dart`)
- End-to-end theme switching
- App startup with Forui themes
- Theme persistence and state management
- Widget rendering without errors

### Running Tests

```bash
# Run all forui migration tests
flutter test test/run_forui_tests.dart

# Run individual test suites
flutter test test/theme_service_forui_test.dart
flutter test test/forui_widget_conversion_test.dart
flutter test test/icon_standardization_test.dart
flutter test test/forui_migration_integration_test.dart
```

## Benefits of Migration

### 1. Consistency
- Unified design system across the entire app
- Consistent spacing, typography, and colors
- Better visual hierarchy and component relationships

### 2. Customization
- More flexible theming system
- Better support for custom themes
- Easier to maintain design consistency

### 3. Performance
- Optimized widget rendering
- Better state management
- Reduced widget tree complexity

### 4. Maintainability
- Cleaner code structure
- Better separation of concerns
- Easier to add new features and themes

## Migration Guidelines

### For Future Development

1. **Always use Forui widgets** for new features
2. **Follow the established patterns** for widget usage
3. **Use semantic icons** that make sense in context
4. **Test theme compatibility** when adding new components
5. **Maintain consistency** with existing Forui implementations

### Widget Usage Patterns

```dart
// Button usage
FButton(
  onPress: () {},
  child: const Text('Button Text'),
)

// Tile usage
FTile(
  title: const Text('Title'),
  subtitle: const Text('Subtitle'),
  prefix: const Icon(Icons.icon_name),
  onPress: () {},
)

// Form field usage
FTextFormField(
  controller: controller,
  label: const Text('Label'),
  validator: (value) => validation,
)

// Card usage
FCard(
  title: 'Card Title',
  subtitle: 'Card Subtitle',
  child: CardContent(),
)
```

## Known Limitations

1. **RadioListTile** - Kept as Material widget due to complex dialog usage
2. **Some Material widgets** - Still used in specific contexts where Forui equivalents don't exist
3. **Custom styling** - May require additional work for highly customized components

## Future Improvements

1. **Complete RadioListTile migration** - Convert to FSelectGroup when dialog structure allows
2. **Custom Forui styles** - Create app-specific widget styles
3. **Animation improvements** - Leverage Forui's animation capabilities
4. **Accessibility enhancements** - Utilize Forui's built-in accessibility features

## Conclusion

The Forui migration significantly improves the app's UI consistency, maintainability, and customization capabilities while preserving all existing functionality. The comprehensive test suite ensures that the migration is robust and future-proof.
